.footer-component {
  background-color: var(--primary-light-color);
  color: var(--secondary-color);
  padding: 60px 0 20px;
}

.footer-component .footer-container {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr;
  gap: 25px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-component .footer-logo-section {
  display: grid;
  gap: 15PX;
}

.footer-component .footer-logo {
  height: 75px;
  aspect-ratio: 1/1;
  
}

.footer-component .icons {
  display: flex;
  gap: 15px;
}

.footer-component .social-icon {
  padding: 5px;
  border-radius: 50%;
  border: 1px solid var(--dark-gray);
  color: var(--secondary-color);
  transition: color 0.3s ease;
  cursor: pointer;
}

.footer-component .social-icon:hover {
  color: var(--btn-color);
}

.footer-component .footer-tagline {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--btn-color);
  margin-bottom: 10px;
}

.footer-component .footer-description {
  font-size: var(--smallfont);
  line-height: 1.5;
  margin-bottom: 20px;
}

.footer-component h3 {
  font-size: var(--heading6);
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--secondary-color);
}

.footer-component ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-component ul li {
  margin-bottom: 10px;
}

.footer-component ul li a {
  border-bottom: 1px dotted var(--secondary-color);
  color: var(--secondary-color);
  text-decoration: none;
  font-size: var(--smallfont);
  transition: color 0.3s ease;
}

.footer-component ul li a:hover {
  color: var(--btn-color);
}

.footer-component .footer-contact p {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: var(--smallfont);
  margin-bottom: 20px;
  color: var(--secondary-color);
}

.footer-component .footer-social {
  display: flex;
  gap: 15px;
  margin-top: 15px;
}

.footer-component .footer-sports-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;

}

.footer-component .footer-social a {
  color: var(--secondary-color);
  font-size: 20px;
  transition: color 0.3s ease;
}

.footer-component .footer-social a:hover {
  color: var(--primary-color);
}

.footer-component .footer-bottom {
  margin: 40px auto 0;
  padding: 20px 20px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
  border-top: 1px solid var(--secondary-color);
}

.footer-component .footer-bottom p {
  font-size: var(--extrasmallfont);
  color: var(--secondary-color);
}
.footer-component .Contact__social-icons {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
}
.footer-component .footericonborder {
  border: 1px solid var(--secondary-color);
  padding: 10px;
  border-radius: 50%;
  cursor: pointer;
}
.footer-component .footer__social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--secondary-color);
  transition: all 0.3s ease;
}
.footer-component .footericonborder:hover {
  transition: all 0.3s ease;
  scale: 1.3;
}
.footer-contact{
gap: 10px;
width: 100%;
display: grid;
  justify-content: flex-start;
  align-items: center;
  margin-top: 10px;
}
.getintouchcss{
  display: flex;
  flex-direction: column;

}
.getintouchcss p{
  display: flex;
gap: 10px;
}
/* Responsive styles */
@media (max-width: 1024px) {
  .footer-component .footer-container {
    display: flex;
    justify-content: space-around;
  }

  .footer-component .footer-sports-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .footer-component {
    padding: 40px 0 15px;
  }

  .footer-component .footer-container {
    grid-template-columns: 1fr;
    gap: 25px;
    display: grid;
  }

  .footer-component .footer-sports-grid {
    grid-template-columns: 1fr 1fr;
  }

  .footer-component .footer-bottom {
    margin-top: 30px;
  }
  .quickLinks {
    display: grid;
    grid-template-columns: 1fr;
  }
}
@media (max-width: 500px) {
  .footer-component .footer-logo-section {
    display: grid;
    justify-content: flex-start;
    align-items: center;
    grid-template-columns: 1fr 1fr;
 
  }

  .footer-component .footer-logo {
    height: 60px;
    aspect-ratio: 1/1;
  }

  .footer-component .footer-links {
    display: grid;
    height: fit-content;
  }

  .footer-component .footer-links ul {
    display: grid;
    grid-template-columns: 1fr;
  }
  .footer-component h3 {
 
  margin-bottom: 10px;
  
}

  .footer-component .footer-sports {
    display: grid;
  }

  .footer-component .footer-sports-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }
  .footer-component .Contact__social-icons {
    display: flex;
    gap: 15px;
    margin-top: 0px;
  }
}
@media (max-width: 350px) {
  .footer-component .footer-logo-section {
    display: grid;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    grid-template-columns: 1fr ;
 
  }}