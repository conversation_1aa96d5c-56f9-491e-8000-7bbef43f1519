const { validationResult } = require('express-validator');
const ErrorResponse = require('../../utils/errorResponse');
const Content = require('../../models/Content');
const User = require('../../models/User');
const Order = require('../../models/Order');
const Review = require('../../models/Review');
const sendSellerNotification = require('../../utils/sendSellerNotification');

// @desc    Get all content with filtering, sorting, and pagination
// @route   GET /api/admin/content
// @access  Private/Admin
exports.getAllContent = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      contentType = '',
      sport = '',
      seller = '',
      sortBy = 'createdAt',
      sortOrder = 'desc',
      dateFrom = '',
      dateTo = ''
    } = req.query;

    // Build query
    let query = {};

    // Search filter
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Status filter
    if (status) {
      query.status = status;
    }

    // Content type filter
    if (contentType) {
      query.contentType = contentType;
    }

    // Sport filter
    if (sport) {
      query.sport = sport;
    }

    // Seller filter
    if (seller) {
      query.seller = seller;
    }

    // Date range filter
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
      if (dateTo) query.createdAt.$lte = new Date(dateTo);
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query
    const content = await Content.find(query)
      .populate('seller', 'firstName lastName email')
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await Content.countDocuments(query);

    // Add additional statistics for each content
    const contentWithStats = await Promise.all(
      content.map(async (item) => {
        const contentObj = item.toObject();

        // Get sales count
        const salesCount = await Order.countDocuments({ content: item._id });
        contentObj.salesCount = salesCount;

        // Get revenue
        const revenue = await Order.aggregate([
          { $match: { content: item._id } },
          { $group: { _id: null, total: { $sum: '$amount' } } }
        ]);
        contentObj.totalRevenue = revenue[0]?.total || 0;

        // Get average rating
        const reviews = await Review.find({ content: item._id });
        if (reviews.length > 0) {
          const avgRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;
          contentObj.averageRating = Math.round(avgRating * 10) / 10;
          contentObj.reviewCount = reviews.length;
        } else {
          contentObj.averageRating = 0;
          contentObj.reviewCount = 0;
        }

        return contentObj;
      })
    );

    res.status(200).json({
      success: true,
      data: {
        content: contentWithStats,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total,
          limit: parseInt(limit)
        }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get content by ID with detailed information
// @route   GET /api/admin/content/:id
// @access  Private/Admin
exports.getContentById = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id)
      .populate('seller', 'firstName lastName email profilePic');

    if (!content) {
      return next(new ErrorResponse('Content not found', 404));
    }

    // Get content statistics
    const orders = await Order.find({ content: content._id })
      .populate('buyer', 'firstName lastName email')
      .sort('-createdAt');

    const reviews = await Review.find({ content: content._id })
      .populate('reviewer', 'firstName lastName')
      .sort('-createdAt');

    const stats = {
      totalSales: orders.length,
      totalRevenue: orders.reduce((sum, order) => sum + order.amount, 0),
      averageRating: reviews.length > 0
        ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
        : 0,
      reviewCount: reviews.length,
      recentOrders: orders.slice(0, 5),
      recentReviews: reviews.slice(0, 5)
    };

    res.status(200).json({
      success: true,
      data: {
        content: content.toObject(),
        stats
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Approve content
// @route   PUT /api/admin/content/:id/approve
// @access  Private/Admin
exports.approveContent = async (req, res, next) => {
  try {
    const { approvalNotes } = req.body;

    const content = await Content.findById(req.params.id);
    if (!content) {
      return next(new ErrorResponse('Content not found', 404));
    }

    const updatedContent = await Content.findByIdAndUpdate(
      req.params.id,
      {
        status: 'Published',
        approvalDate: new Date(),
        approvalNotes,
        approvedBy: req.user.id
      },
      { new: true }
    ).populate('seller', 'firstName lastName email');

    // Send notification to seller about approval in background (don't await)
    sendSellerNotification({
      sellerId: updatedContent.seller._id,
      sellerEmail: updatedContent.seller.email,
      sellerName: `${updatedContent.seller.firstName} ${updatedContent.seller.lastName}`,
      contentTitle: updatedContent.title,
      contentId: updatedContent._id,
      type: 'approval',
      notes: approvalNotes || 'Your content meets our quality standards and has been approved.'
    }).then(() => {
      console.log(`Notification sent to seller ${updatedContent.seller.email} for content approval`);
    }).catch((notificationError) => {
      console.error('Failed to send seller notification:', notificationError);
      // Email failure doesn't affect approval response
    });

    res.status(200).json({
      success: true,
      data: updatedContent
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Reject content
// @route   PUT /api/admin/content/:id/reject
// @access  Private/Admin
exports.rejectContent = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { reason, rejectionNotes } = req.body;

    const content = await Content.findById(req.params.id);
    if (!content) {
      return next(new ErrorResponse('Content not found', 404));
    }

    const updatedContent = await Content.findByIdAndUpdate(
      req.params.id,
      {
        status: 'Draft',
        rejectionReason: reason,
        rejectionNotes,
        rejectionDate: new Date(),
        rejectedBy: req.user.id
      },
      { new: true }
    ).populate('seller', 'firstName lastName email');

    // Send notification to seller about rejection in background (don't await)
    sendSellerNotification({
      sellerId: updatedContent.seller._id,
      sellerEmail: updatedContent.seller.email,
      sellerName: `${updatedContent.seller.firstName} ${updatedContent.seller.lastName}`,
      contentTitle: updatedContent.title,
      contentId: updatedContent._id,
      type: 'rejection',
      reason: reason,
      notes: rejectionNotes
    }).then(() => {
      console.log(`Rejection notification sent to seller ${updatedContent.seller.email}`);
    }).catch((notificationError) => {
      console.error('Failed to send seller rejection notification:', notificationError);
      // Email failure doesn't affect rejection response
    });

    res.status(200).json({
      success: true,
      data: updatedContent
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete content
// @route   DELETE /api/admin/content/:id
// @access  Private/Admin
exports.deleteContent = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id);

    if (!content) {
      return next(new ErrorResponse('Content not found', 404));
    }

    // Check if content has orders
    const hasOrders = await Order.countDocuments({ content: content._id });

    if (hasOrders > 0) {
      // Soft delete - archive instead of removing
      await Content.findByIdAndUpdate(req.params.id, {
        status: 'Archived',
        archivedAt: new Date(),
        archivedBy: req.user.id
      });
    } else {
      // Hard delete if no orders
      await Content.findByIdAndDelete(req.params.id);
    }

    res.status(200).json({
      success: true,
      message: 'Content deleted successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk approve content
// @route   POST /api/admin/content/bulk-approve
// @access  Private/Admin
exports.bulkApproveContent = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { contentIds } = req.body;

    const result = await Content.updateMany(
      { _id: { $in: contentIds } },
      {
        status: 'Published',
        approvalDate: new Date(),
        approvedBy: req.user.id
      }
    );

    // Send notifications to sellers for each approved content
    // Get the updated content items with seller information
    const approvedContent = await Content.find({ _id: { $in: contentIds } })
      .populate('seller', 'firstName lastName email');

    // Send notifications in the background (don't await)
    approvedContent.forEach(content => {
      sendSellerNotification({
        sellerId: content.seller._id,
        sellerEmail: content.seller.email,
        sellerName: `${content.seller.firstName} ${content.seller.lastName}`,
        contentTitle: content.title,
        contentId: content._id,
        type: 'approval',
        notes: 'Your content meets our quality standards and has been approved.'
      }).catch(error => {
        console.error(`Failed to send approval notification for content ${content._id}:`, error);
      });
    });

    res.status(200).json({
      success: true,
      message: `${result.modifiedCount} content items approved successfully`,
      data: {
        matched: result.matchedCount,
        modified: result.modifiedCount
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk reject content
// @route   POST /api/admin/content/bulk-reject
// @access  Private/Admin
exports.bulkRejectContent = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { contentIds, reason } = req.body;

    const result = await Content.updateMany(
      { _id: { $in: contentIds } },
      {
        status: 'Rejected',
        rejectionReason: reason,
        rejectionDate: new Date(),
        rejectedBy: req.user.id
      }
    );

    res.status(200).json({
      success: true,
      message: `${result.modifiedCount} content items rejected successfully`,
      data: {
        matched: result.matchedCount,
        modified: result.modifiedCount
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk delete content
// @route   POST /api/admin/content/bulk-delete
// @access  Private/Admin
exports.bulkDeleteContent = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { contentIds } = req.body;

    let softDeleted = 0;
    let hardDeleted = 0;

    for (const contentId of contentIds) {
      const content = await Content.findById(contentId);
      if (!content) continue;

      const hasOrders = await Order.countDocuments({ content: contentId });

      if (hasOrders > 0) {
        // Soft delete
        await Content.findByIdAndUpdate(contentId, {
          status: 'Archived',
          archivedAt: new Date(),
          archivedBy: req.user.id
        });
        softDeleted++;
      } else {
        // Hard delete
        await Content.findByIdAndDelete(contentId);
        hardDeleted++;
      }
    }

    res.status(200).json({
      success: true,
      message: `${softDeleted + hardDeleted} content items deleted successfully`,
      data: {
        softDeleted,
        hardDeleted,
        total: softDeleted + hardDeleted
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get content statistics
// @route   GET /api/admin/content/stats
// @access  Private/Admin
exports.getContentStats = async (req, res, next) => {
  try {
    const totalContent = await Content.countDocuments();
    const publishedContent = await Content.countDocuments({ status: 'Published' });
    const pendingContent = await Content.countDocuments({ status: 'Under Review' });
    const rejectedContent = await Content.countDocuments({ status: 'Rejected' });
    const draftContent = await Content.countDocuments({ status: 'Draft' });

    const contentByType = await Content.aggregate([
      {
        $group: {
          _id: '$contentType',
          count: { $sum: 1 }
        }
      }
    ]);

    const contentBySport = await Content.aggregate([
      {
        $group: {
          _id: '$sport',
          count: { $sum: 1 }
        }
      }
    ]);

    // New content in last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const newContent = await Content.countDocuments({
      createdAt: { $gte: thirtyDaysAgo }
    });

    res.status(200).json({
      success: true,
      data: {
        total: totalContent,
        published: publishedContent,
        pending: pendingContent,
        rejected: rejectedContent,
        draft: draftContent,
        newInLast30Days: newContent,
        byType: contentByType,
        bySport: contentBySport
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Export content data
// @route   GET /api/admin/content/export
// @access  Private/Admin
exports.exportContent = async (req, res, next) => {
  try {
    const { format = 'csv', status = '', contentType = '', sport = '' } = req.query;

    let query = {};
    if (status) query.status = status;
    if (contentType) query.contentType = contentType;
    if (sport) query.sport = sport;

    const content = await Content.find(query)
      .populate('seller', 'firstName lastName email')
      .sort('-createdAt');

    // Add statistics to each content item
    const contentWithStats = await Promise.all(
      content.map(async (item) => {
        const contentObj = item.toObject();

        const salesCount = await Order.countDocuments({ content: item._id });
        const revenue = await Order.aggregate([
          { $match: { content: item._id } },
          { $group: { _id: null, total: { $sum: '$amount' } } }
        ]);

        contentObj.salesCount = salesCount;
        contentObj.totalRevenue = revenue[0]?.total || 0;

        return contentObj;
      })
    );

    if (format === 'json') {
      res.status(200).json({
        success: true,
        data: contentWithStats
      });
    } else {
      // For CSV format, you would implement CSV generation here
      res.status(200).json({
        success: true,
        message: 'CSV export functionality to be implemented',
        data: contentWithStats
      });
    }
  } catch (err) {
    next(err);
  }
};

// @desc    Update content status
// @route   PUT /api/admin/content/:id/status
// @access  Private/Admin
exports.updateContentStatus = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { status } = req.body;

    const content = await Content.findById(req.params.id);
    if (!content) {
      return next(new ErrorResponse('Content not found', 404));
    }

    // Check if content is an auction and handle special cases
    if (content.isAuction) {
      const now = new Date();

      // Don't allow status changes for sold items
      if (content.isSold) {
        return next(new ErrorResponse('Cannot change status of sold content', 400));
      }

      // Don't allow publishing if auction has ended
      if (status === 'Published' && content.auctionEndDate && content.auctionEndDate < now) {
        return next(new ErrorResponse('Cannot publish expired auction', 400));
      }
    }

    // Update the content status
    const updatedContent = await Content.findByIdAndUpdate(
      req.params.id,
      {
        status,
        updatedBy: req.user.id,
        $push: {
          statusHistory: {
            status,
            changedBy: req.user.id,
            changedAt: new Date(),
            reason: req.body.reason || 'Status updated by admin'
          }
        }
      },
      { new: true }
    ).populate('seller', 'firstName lastName email')
      .populate('statusHistory.changedBy', 'firstName lastName email');

    // Add additional auction status information to response
    const response = updatedContent.toObject();
    if (updatedContent.isAuction) {
      response.auctionStatus = updatedContent.auctionStatus;
      response.isAuctionActive = updatedContent.isAuctionActive();
    }

    res.status(200).json({
      success: true,
      data: response
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get content reviews
// @route   GET /api/admin/content/:id/reviews
// @access  Private/Admin
exports.getContentReviews = async (req, res, next) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const content = await Content.findById(req.params.id);
    if (!content) {
      return next(new ErrorResponse('Content not found', 404));
    }

    const reviews = await Review.find({ content: req.params.id })
      .populate('reviewer', 'firstName lastName email')
      .sort('-createdAt')
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Review.countDocuments({ content: req.params.id });

    res.status(200).json({
      success: true,
      data: {
        reviews,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total,
          limit: parseInt(limit)
        }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Moderate content
// @route   PUT /api/admin/content/:id/moderate
// @access  Private/Admin
exports.moderateContent = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { action, reason } = req.body;

    const content = await Content.findById(req.params.id);
    if (!content) {
      return next(new ErrorResponse('Content not found', 404));
    }

    let updateData = {};

    switch (action) {
      case 'flag':
        updateData = {
          isFlagged: true,
          flagReason: reason,
          flaggedAt: new Date(),
          flaggedBy: req.user.id
        };
        break;
      case 'unflag':
        updateData = {
          isFlagged: false,
          $unset: { flagReason: 1, flaggedAt: 1, flaggedBy: 1 }
        };
        break;
      case 'restrict':
        updateData = {
          isRestricted: true,
          restrictionReason: reason,
          restrictedAt: new Date(),
          restrictedBy: req.user.id
        };
        break;
      case 'unrestrict':
        updateData = {
          isRestricted: false,
          $unset: { restrictionReason: 1, restrictedAt: 1, restrictedBy: 1 }
        };
        break;
      default:
        return next(new ErrorResponse('Invalid moderation action', 400));
    }

    const updatedContent = await Content.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true }
    ).populate('seller', 'firstName lastName email');

    res.status(200).json({
      success: true,
      data: updatedContent
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Feature content
// @route   PUT /api/admin/content/:id/feature
// @access  Private/Admin
exports.featureContent = async (req, res, next) => {
  try {
    const content = await Content.findByIdAndUpdate(
      req.params.id,
      {
        isFeatured: true,
        featuredAt: new Date(),
        featuredBy: req.user.id
      },
      { new: true }
    ).populate('seller', 'firstName lastName email');

    if (!content) {
      return next(new ErrorResponse('Content not found', 404));
    }

    res.status(200).json({
      success: true,
      data: content
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Unfeature content
// @route   PUT /api/admin/content/:id/unfeature
// @access  Private/Admin
exports.unfeatureContent = async (req, res, next) => {
  try {
    const content = await Content.findByIdAndUpdate(
      req.params.id,
      {
        isFeatured: false,
        $unset: { featuredAt: 1, featuredBy: 1 }
      },
      { new: true }
    ).populate('seller', 'firstName lastName email');

    if (!content) {
      return next(new ErrorResponse('Content not found', 404));
    }

    res.status(200).json({
      success: true,
      data: content
    });
  } catch (err) {
    next(err);
  }
};
